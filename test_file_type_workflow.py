#!/usr/bin/env python3
"""测试基于file_type的工作流选择功能"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_file_type_workflow():
    """测试基于file_type的工作流选择功能"""
    try:
        # 测试导入
        from app.core.config import settings
        from app.enums.workflow_types import WorkflowType
        from app.services.dify_service import DifyService
        from app.dependencies.common import get_sop_service_by_file_type
        
        print("✓ 所有模块导入成功")
        
        # 测试工作流类型枚举
        print(f"✓ 所有工作流类型: {WorkflowType.get_all_types()}")
        
        # 测试根据file_type获取工作流类型
        workflow_type_1 = WorkflowType.get_sop_workflow_by_file_type(1)
        workflow_type_2 = WorkflowType.get_sop_workflow_by_file_type(2)
        workflow_type_invalid = WorkflowType.get_sop_workflow_by_file_type(999)
        
        print(f"✓ file_type=1 对应工作流: {workflow_type_1}")
        print(f"✓ file_type=2 对应工作流: {workflow_type_2}")
        print(f"✓ file_type=999 对应工作流: {workflow_type_invalid}")
        
        # 测试配置获取
        for workflow_type in [WorkflowType.SOP_TYPE1, WorkflowType.SOP_TYPE2, WorkflowType.SOP]:
            config = settings.get_workflow_config(workflow_type)
            print(f"✓ {workflow_type} 配置: {config}")
        
        # 测试DifyService实例化
        try:
            service_type1 = DifyService(WorkflowType.SOP_TYPE1)
            print(f"✓ SOP Type1 DifyService创建成功，工作流类型: {service_type1.workflow_type}")
        except Exception as e:
            print(f"⚠ SOP Type1 DifyService创建失败: {e}")
        
        try:
            service_type2 = DifyService(WorkflowType.SOP_TYPE2)
            print(f"✓ SOP Type2 DifyService创建成功，工作流类型: {service_type2.workflow_type}")
        except Exception as e:
            print(f"⚠ SOP Type2 DifyService创建失败: {e}")
        
        # 测试基于file_type的SOP服务创建
        try:
            sop_service_1 = get_sop_service_by_file_type(1)
            print(f"✓ file_type=1 SOP服务创建成功，工作流类型: {sop_service_1.dify_service.workflow_type}")
        except Exception as e:
            print(f"⚠ file_type=1 SOP服务创建失败: {e}")
        
        try:
            sop_service_2 = get_sop_service_by_file_type(2)
            print(f"✓ file_type=2 SOP服务创建成功，工作流类型: {sop_service_2.dify_service.workflow_type}")
        except Exception as e:
            print(f"⚠ file_type=2 SOP服务创建失败: {e}")
        
        print("\n🎉 所有测试通过！基于file_type的工作流选择功能正常工作。")
        
        print("\n📋 功能说明:")
        print("- file_type=1 将使用 sop_type1 工作流")
        print("- file_type=2 将使用 sop_type2 工作流")
        print("- 其他file_type值将回退到通用 sop 工作流")
        print("- 配置优先级: 专用配置 > SOP通用配置 > 默认配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_file_type_workflow()
    sys.exit(0 if success else 1)
