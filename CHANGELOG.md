# 更新日志

## [2025-01-01] 基于file_type的工作流选择功能

### 新增功能

#### 🎯 核心功能：基于file_type的自动工作流选择
- **自动工作流选择**：SOP上传接口现在会根据`file_type`参数自动选择对应的工作流
  - `file_type=1` → 使用 `sop_type1` 工作流
  - `file_type=2` → 使用 `sop_type2` 工作流
  - 其他值 → 使用通用 `sop` 工作流

#### 🔧 配置系统增强
- **新增工作流类型**：
  - `sop_type1`: SOP Type1工作流 (file_type=1)
  - `sop_type2`: SOP Type2工作流 (file_type=2)
- **新增环境变量**：
  - `DIFY_SOP_TYPE1_API_KEY`, `DIFY_SOP_TYPE1_API_URL`, `DIFY_SOP_TYPE1_USER`
  - `DIFY_SOP_TYPE2_API_KEY`, `DIFY_SOP_TYPE2_API_URL`, `DIFY_SOP_TYPE2_USER`
- **配置优先级系统**：专用配置 > SOP通用配置 > 默认配置

#### 🌐 API端点增强
- **新增工作流管理端点**：
  - `GET /api/v1/workflow/types` - 获取所有支持的工作流类型
  - `GET /api/v1/workflow/config/{type}` - 获取特定工作流的配置状态
- **SOP上传端点增强**：现在会根据`file_type`参数自动选择工作流

### 技术改进

#### 📦 代码结构优化
- **新增枚举类**：`app/enums/workflow_types.py` - 统一管理工作流类型
- **依赖注入增强**：新增 `get_sop_service_by_file_type()` 函数
- **配置管理优化**：`Settings.get_workflow_config()` 方法支持新的工作流类型

#### 🔒 安全性和稳定性
- **配置验证**：启动时验证必要的API密钥配置
- **错误处理**：明确的错误信息和异常处理
- **向后兼容**：完全兼容现有的SOP功能

### 文件变更

#### 新增文件
- `app/enums/__init__.py` - 枚举包初始化
- `app/enums/workflow_types.py` - 工作流类型枚举
- `app/api/v1/workflow.py` - 工作流管理API端点
- `docs/workflow_configuration.md` - 工作流配置文档

#### 修改文件
- `app/core/config.py` - 新增工作流配置支持
- `app/services/dify_service.py` - 支持工作流类型参数
- `app/dependencies/common.py` - 新增基于file_type的依赖注入
- `app/api/v1/sop.py` - 使用新的工作流选择机制
- `app/api/v1/routers.py` - 添加工作流管理路由
- `app/main.py` - 修复API路由前缀
- `.env.example` - 新增配置示例

### 使用示例

#### 配置示例
```bash
# 为不同file_type配置不同的Dify应用
DIFY_SOP_TYPE1_API_KEY=app-xxx-for-type1
DIFY_SOP_TYPE2_API_KEY=app-yyy-for-type2
```

#### API调用示例
```bash
# file_type=1 会使用 sop_type1 工作流
curl -X POST "/api/v1/sop/upload" \
  -F "file=@example.xlsx" \
  -F "file_type=1" \
  -F "max_len=40"

# file_type=2 会使用 sop_type2 工作流  
curl -X POST "/api/v1/sop/upload" \
  -F "file=@example.xlsx" \
  -F "file_type=2" \
  -F "max_len=40"
```

### 兼容性说明

- ✅ **完全向后兼容**：现有的SOP功能无需任何修改
- ✅ **配置兼容**：如果没有配置专用API密钥，会自动回退到通用配置
- ✅ **API兼容**：现有的API调用方式完全不变

### 部署注意事项

1. **环境变量**：根据需要配置新的 `DIFY_SOP_TYPE1_*` 和 `DIFY_SOP_TYPE2_*` 环境变量
2. **API密钥权限**：确保新的API密钥具有适当的Dify应用访问权限
3. **监控**：可通过 `/api/v1/workflow/config/{type}` 端点监控各工作流的配置状态

### 测试验证

所有功能已通过完整测试验证：
- ✅ 模块导入和初始化
- ✅ 工作流类型枚举和映射
- ✅ 配置系统和优先级
- ✅ DifyService实例化
- ✅ 基于file_type的服务创建
- ✅ API端点功能
- ✅ 配置优先级和回退机制
- ✅ 功能完整性验证
