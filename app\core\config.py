import logging
from typing import Dict, Literal

from pydantic import Field, field_validator, model_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    ENVIRONMENT: Literal["development", "testing", "production"] = Field(
        default="development", description="运行环境"
    )
    DEBUG: bool = Field(default=True, description="调试模式")
    VERSION: str = Field(default="1.0.0", description="应用版本")

    HOST: str = Field(default="0.0.0.0", description="服务主机")
    PORT: int = Field(default=30101, ge=1, le=65535, description="服务端口")

    LOG_LEVEL: str = Field(default="INFO", description="日志级别")
    LOG_FILE_MAX_SIZE: int = Field(
        default=10 * 1024 * 1024, description="日志文件最大大小(字节)"
    )
    LOG_FILE_BACKUP_COUNT: int = Field(default=5, description="日志文件备份数量")

    # 默认Dify配置（向后兼容）
    DIFY_API_KEY: str = Field(default="", description="Dify API 密钥")
    DIFY_API_URL: str = Field(default="", description="Dify API 地址")
    DIFY_USER: str = Field(default="admin", description="Dify 用户")

    # SOP工作流专用配置
    DIFY_SOP_API_KEY: str = Field(default="", description="SOP工作流 Dify API 密钥")
    DIFY_SOP_API_URL: str = Field(default="", description="SOP工作流 Dify API 地址")
    DIFY_SOP_USER: str = Field(default="admin", description="SOP工作流 Dify 用户")

    # SOP Type1 工作流专用配置 (file_type=1)
    DIFY_SOP_TYPE1_API_KEY: str = Field(default="", description="SOP Type1工作流 Dify API 密钥")
    DIFY_SOP_TYPE1_API_URL: str = Field(default="", description="SOP Type1工作流 Dify API 地址")
    DIFY_SOP_TYPE1_USER: str = Field(default="admin", description="SOP Type1工作流 Dify 用户")

    # SOP Type2 工作流专用配置 (file_type=2)
    DIFY_SOP_TYPE2_API_KEY: str = Field(default="", description="SOP Type2工作流 Dify API 密钥")
    DIFY_SOP_TYPE2_API_URL: str = Field(default="", description="SOP Type2工作流 Dify API 地址")
    DIFY_SOP_TYPE2_USER: str = Field(default="admin", description="SOP Type2工作流 Dify 用户")

    @field_validator("LOG_LEVEL")
    @classmethod
    def validate_log_level(cls, v):
        """验证日志级别"""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"LOG_LEVEL必须是以下值之一: {', '.join(valid_levels)}")
        return v.upper()

    @model_validator(mode="after")
    def validate_environment_settings(self):
        """验证环境相关配置"""
        if self.ENVIRONMENT == "production" and self.DEBUG:
            logging.warning("生产环境不建议开启调试模式")

        if self.ENVIRONMENT != "development":
            # 检查默认配置或任一SOP专用配置
            has_api_key = any([
                self.DIFY_API_KEY,
                self.DIFY_SOP_API_KEY,
                self.DIFY_SOP_TYPE1_API_KEY,
                self.DIFY_SOP_TYPE2_API_KEY
            ])
            has_api_url = any([
                self.DIFY_API_URL,
                self.DIFY_SOP_API_URL,
                self.DIFY_SOP_TYPE1_API_URL,
                self.DIFY_SOP_TYPE2_API_URL
            ])

            if not has_api_key:
                raise ValueError("非开发环境必须配置至少一个DIFY_API_KEY")
            if not has_api_url:
                raise ValueError("非开发环境必须配置至少一个DIFY_API_URL")

        return self

    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.ENVIRONMENT == "development"

    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.ENVIRONMENT == "production"

    @property
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.ENVIRONMENT == "testing"

    def get_workflow_config(self, workflow_type: str) -> Dict[str, str]:
        """获取指定工作流的配置"""
        workflow_configs = {
            "sop": {
                "api_key": self.DIFY_SOP_API_KEY or self.DIFY_API_KEY,
                "api_url": self.DIFY_SOP_API_URL or self.DIFY_API_URL,
                "user": self.DIFY_SOP_USER or self.DIFY_USER,
            },
            "sop_type1": {
                "api_key": self.DIFY_SOP_TYPE1_API_KEY or self.DIFY_SOP_API_KEY or self.DIFY_API_KEY,
                "api_url": self.DIFY_SOP_TYPE1_API_URL or self.DIFY_SOP_API_URL or self.DIFY_API_URL,
                "user": self.DIFY_SOP_TYPE1_USER or self.DIFY_SOP_USER or self.DIFY_USER,
            },
            "sop_type2": {
                "api_key": self.DIFY_SOP_TYPE2_API_KEY or self.DIFY_SOP_API_KEY or self.DIFY_API_KEY,
                "api_url": self.DIFY_SOP_TYPE2_API_URL or self.DIFY_SOP_API_URL or self.DIFY_API_URL,
                "user": self.DIFY_SOP_TYPE2_USER or self.DIFY_SOP_USER or self.DIFY_USER,
            },
            "default": {
                "api_key": self.DIFY_API_KEY,
                "api_url": self.DIFY_API_URL,
                "user": self.DIFY_USER,
            },
        }

        return workflow_configs.get(workflow_type, workflow_configs["default"])

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        env_prefix = ""
        extra = "ignore"


def get_settings() -> Settings:
    """获取配置实例"""
    try:
        return Settings()
    except Exception as e:
        raise ValueError(f"配置加载失败: {str(e)}")


# 全局配置实例
settings = get_settings()
