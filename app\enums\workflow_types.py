from enum import Enum


class WorkflowType(str, Enum):
    """工作流类型枚举"""

    SOP = "sop"
    SOP_TYPE1 = "sop_type1"  # file_type=1 的SOP工作流
    SOP_TYPE2 = "sop_type2"  # file_type=2 的SOP工作流
    DEFAULT = "default"

    @classmethod
    def get_all_types(cls):
        """获取所有工作流类型"""
        return [workflow_type.value for workflow_type in cls]

    @classmethod
    def get_sop_workflow_by_file_type(cls, file_type: int) -> str:
        """根据file_type获取对应的SOP工作流类型"""
        if file_type == 1:
            return cls.SOP_TYPE1
        elif file_type == 2:
            return cls.SOP_TYPE2
        else:
            return cls.SOP  # 默认回退到通用SOP工作流
