#!/usr/bin/env python3
"""测试基于file_type的API端点功能"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_api_file_type():
    """测试基于file_type的API端点功能"""
    try:
        from fastapi.testclient import TestClient
        from app.main import app
        
        client = TestClient(app)
        
        print("测试基于file_type的API端点功能...")
        
        # 测试获取工作流类型（应该包含新的类型）
        response = client.get("/api/v1/workflow/types")
        print(f"✓ 工作流类型端点状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            workflow_types = data.get("workflow_types", [])
            descriptions = data.get("descriptions", {})
            
            print(f"✓ 工作流类型: {workflow_types}")
            print(f"✓ 工作流描述: {descriptions}")
            
            # 验证新的工作流类型是否存在
            expected_types = ["sop", "sop_type1", "sop_type2", "default"]
            for expected_type in expected_types:
                if expected_type in workflow_types:
                    print(f"✓ 工作流类型 '{expected_type}' 存在")
                else:
                    print(f"❌ 工作流类型 '{expected_type}' 不存在")
        else:
            print(f"❌ 工作流类型端点失败: {response.text}")
        
        # 测试获取各种工作流配置
        for workflow_type in ["sop_type1", "sop_type2", "sop"]:
            response = client.get(f"/api/v1/workflow/config/{workflow_type}")
            print(f"✓ {workflow_type}配置端点状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"✓ {workflow_type}配置: {data}")
            else:
                print(f"❌ {workflow_type}配置端点失败: {response.text}")
        
        # 测试健康检查端点
        response = client.get("/health")
        print(f"✓ 健康检查端点状态码: {response.status_code}")
        
        print("\n🎉 所有API端点测试通过！")
        
        print("\n📋 API使用说明:")
        print("- POST /api/v1/sop/upload 端点现在会根据file_type参数自动选择工作流:")
        print("  - file_type=1 使用 sop_type1 工作流")
        print("  - file_type=2 使用 sop_type2 工作流")
        print("  - 其他值使用通用 sop 工作流")
        print("- GET /api/v1/workflow/types 查看所有支持的工作流类型")
        print("- GET /api/v1/workflow/config/{type} 查看特定工作流的配置状态")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_api_file_type()
    sys.exit(0 if success else 1)
