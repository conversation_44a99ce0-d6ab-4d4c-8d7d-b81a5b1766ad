import json
import re


class JSONParser:
    def __init__(self):
        self.patterns = [
            # markdown代码块
            r"```(?:json)?\s*(\{.*?\}|\[.*?\])\s*```",
            # 裸露的JSON对象
            r"(\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})",
            # 裸露的JSON数组
            r"(\[[^\[\]]*(?:\[[^\[\]]*\][^\[\]]*)*\])",
        ]

        # 思考块的正则表达式模式
        self.thinking_patterns = [
            # 标准思考块格式
            r'<details\s+style="[^"]*"\s+open>\s*<summary>\s*Thinking\.\.\.\s*</summary>\s*.*?</details>\s*',
            # 简化的思考块格式
            r"<details[^>]*>\s*<summary>\s*Thinking\.\.\.\s*</summary>\s*.*?</details>\s*",
            # 更通用的思考块格式
            r"<details[^>]*>\s*<summary>[^<]*(?:thinking|思考)[^<]*</summary>\s*.*?</details>\s*",
        ]

    def remove_thinking_blocks(self, text):
        """移除文本中的思考块"""
        cleaned_text = text

        for pattern in self.thinking_patterns:
            # 使用 DOTALL 标志使 . 匹配换行符，IGNORECASE 忽略大小写
            cleaned_text = re.sub(
                pattern, "", cleaned_text, flags=re.DOTALL | re.IGNORECASE
            )

        return cleaned_text.strip()

    def extract_json_with_braces(self, text):
        """使用括号计数提取完整的JSON对象"""
        # 提取JSON对象 {}
        brace_count = 0
        start_idx = -1

        for i, char in enumerate(text):
            if char == "{":
                if start_idx == -1:
                    start_idx = i
                brace_count += 1
            elif char == "}":
                brace_count -= 1
                if brace_count == 0 and start_idx != -1:
                    json_str = text[start_idx : i + 1]
                    try:
                        return json.loads(json_str)
                    except json.JSONDecodeError:
                        pass
                    start_idx = -1

        return None

    def extract_json_with_brackets(self, text):
        """使用括号计数提取完整的JSON数组"""
        # 提取JSON数组 []
        bracket_count = 0
        start_idx = -1

        for i, char in enumerate(text):
            if char == "[":
                if start_idx == -1:
                    start_idx = i
                bracket_count += 1
            elif char == "]":
                bracket_count -= 1
                if bracket_count == 0 and start_idx != -1:
                    json_str = text[start_idx : i + 1]
                    try:
                        return json.loads(json_str)
                    except json.JSONDecodeError:
                        pass
                    start_idx = -1

        return None

    def parse(self, text):
        """解析文本中的JSON"""
        if not text or not isinstance(text, str):
            raise ValueError("输入必须是非空字符串")

        # 步骤1：移除思考块
        cleaned_text = self.remove_thinking_blocks(text)

        # 步骤2：直接解析整个文本
        try:
            return json.loads(cleaned_text.strip())
        except json.JSONDecodeError:
            pass

        # 步骤3：移除可能的markdown标记后再次尝试
        # 移除 ```json 和 ```
        markdown_cleaned = re.sub(
            r"^```(?:json)?\s*", "", cleaned_text, flags=re.IGNORECASE | re.MULTILINE
        )
        markdown_cleaned = re.sub(r"\s*```$", "", markdown_cleaned, flags=re.MULTILINE)

        try:
            return json.loads(markdown_cleaned.strip())
        except json.JSONDecodeError:
            pass

        # 步骤4：使用正则表达式模式匹配
        for pattern in self.patterns:
            matches = re.findall(pattern, cleaned_text, re.DOTALL | re.IGNORECASE)
            for match in matches:
                try:
                    return json.loads(match.strip())
                except json.JSONDecodeError:
                    continue

        # 步骤5：使用括号计数方法提取JSON对象
        json_obj = self.extract_json_with_braces(cleaned_text)
        if json_obj is not None:
            return json_obj

        # 步骤6：使用括号计数方法提取JSON数组
        json_arr = self.extract_json_with_brackets(cleaned_text)
        if json_arr is not None:
            return json_arr

        raise ValueError(
            f"无法从文本中提取有效的JSON。清理后的文本前100字符: {cleaned_text[:100]}..."
        )

    def parse_safe(self, text, default=None):
        """安全解析，失败时返回默认值"""
        try:
            return self.parse(text)
        except (ValueError, TypeError) as e:
            if default is not None:
                return default
            return {"error": str(e), "raw_text": text}
