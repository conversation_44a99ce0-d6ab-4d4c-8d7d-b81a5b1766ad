# 工作流配置指南

本文档介绍如何配置和使用不同工作流的API密钥功能，特别是基于file_type参数的自动工作流选择。

## 概述

系统现在支持为不同的工作流配置不同的Dify API密钥，特别是：
- **基于file_type的自动工作流选择**：SOP上传接口会根据file_type参数自动选择对应的工作流
- 为不同的业务场景使用不同的Dify应用
- 提高安全性和资源隔离
- 支持多租户场景

## 核心功能：基于file_type的工作流选择

当调用 `/api/v1/sop/upload` 接口时，系统会根据 `file_type` 参数自动选择对应的工作流：

- `file_type=1` → 使用 `sop_type1` 工作流
- `file_type=2` → 使用 `sop_type2` 工作流  
- 其他值 → 使用通用 `sop` 工作流

## 配置方式

### 环境变量配置

在`.env`文件中配置以下变量：

```bash
# 默认Dify配置（向后兼容）
DIFY_API_KEY=your_default_dify_api_key_here
DIFY_API_URL=https://api.dify.ai
DIFY_USER=admin

# SOP工作流专用配置（可选，如果不配置则使用默认配置）
DIFY_SOP_API_KEY=your_sop_dify_api_key_here
DIFY_SOP_API_URL=https://api.dify.ai
DIFY_SOP_USER=admin

# SOP Type1工作流专用配置 (file_type=1)（可选）
DIFY_SOP_TYPE1_API_KEY=your_sop_type1_dify_api_key_here
DIFY_SOP_TYPE1_API_URL=https://api.dify.ai
DIFY_SOP_TYPE1_USER=admin

# SOP Type2工作流专用配置 (file_type=2)（可选）
DIFY_SOP_TYPE2_API_KEY=your_sop_type2_dify_api_key_here
DIFY_SOP_TYPE2_API_URL=https://api.dify.ai
DIFY_SOP_TYPE2_USER=admin
```

### 配置优先级

配置的优先级从高到低为：

1. **专用工作流配置**：`DIFY_SOP_TYPE1_*` 或 `DIFY_SOP_TYPE2_*`
2. **SOP通用配置**：`DIFY_SOP_*`
3. **默认配置**：`DIFY_*`

例如，对于 `file_type=1` 的请求：
- 如果配置了 `DIFY_SOP_TYPE1_API_KEY`，则使用该配置
- 如果没有配置 `DIFY_SOP_TYPE1_API_KEY` 但配置了 `DIFY_SOP_API_KEY`，则使用SOP通用配置
- 如果都没有配置，则使用默认的 `DIFY_API_KEY`

## 支持的工作流类型

目前支持以下工作流类型：

- `sop`: SOP标准作业程序工作流（通用）
- `sop_type1`: SOP Type1工作流 (file_type=1)
- `sop_type2`: SOP Type2工作流 (file_type=2)
- `default`: 默认工作流

## API端点

### SOP文件上传（自动工作流选择）

```http
POST /api/v1/sop/upload
Content-Type: multipart/form-data

file: [Excel文件]
max_len: 40
file_type: 1  # 1或2，决定使用哪个工作流
```

系统会根据 `file_type` 参数自动选择对应的工作流和API密钥。

### 获取工作流类型

```http
GET /api/v1/workflow/types
```

响应示例：
```json
{
  "workflow_types": ["sop", "sop_type1", "sop_type2", "default"],
  "descriptions": {
    "sop": "SOP标准作业程序工作流",
    "sop_type1": "SOP Type1工作流 (file_type=1)",
    "sop_type2": "SOP Type2工作流 (file_type=2)",
    "default": "默认工作流"
  }
}
```

### 获取工作流配置

```http
GET /api/v1/workflow/config/{workflow_type}
```

响应示例：
```json
{
  "workflow_type": "sop_type1",
  "api_url": "https://api.dify.ai",
  "user": "admin",
  "api_key_configured": true,
  "status": "configured"
}
```

## 使用示例

### 场景1：为不同file_type配置不同的Dify应用

```bash
# .env 配置
DIFY_SOP_TYPE1_API_KEY=app-xxx-for-type1
DIFY_SOP_TYPE1_API_URL=https://api.dify.ai

DIFY_SOP_TYPE2_API_KEY=app-yyy-for-type2
DIFY_SOP_TYPE2_API_URL=https://api.dify.ai
```

### 场景2：只为特定file_type配置专用应用

```bash
# .env 配置
DIFY_API_KEY=app-default
DIFY_API_URL=https://api.dify.ai

DIFY_SOP_TYPE1_API_KEY=app-special-for-type1
DIFY_SOP_TYPE1_API_URL=https://api.dify.ai
# file_type=2 会使用默认配置
```

## 代码示例

### 手动创建特定工作流的服务

```python
from app.services.dify_service import DifyService
from app.enums.workflow_types import WorkflowType

# 创建Type1工作流服务
type1_service = DifyService(WorkflowType.SOP_TYPE1)

# 创建Type2工作流服务
type2_service = DifyService(WorkflowType.SOP_TYPE2)
```

### 根据file_type获取服务

```python
from app.dependencies.common import get_sop_service_by_file_type

# 根据file_type自动选择工作流
service = get_sop_service_by_file_type(file_type=1)  # 使用sop_type1工作流
```

## 注意事项

1. **向后兼容性**: 现有的SOP功能完全兼容，无需修改现有代码
2. **配置验证**: 系统会在启动时验证必要的配置是否存在
3. **错误处理**: 如果API密钥未配置，系统会抛出明确的错误信息
4. **缓存**: 依赖注入使用了缓存，相同类型的服务实例会被复用

## 部署注意事项

在生产环境部署时，确保：
1. 所有必要的环境变量都已正确配置
2. API密钥具有适当的权限
3. 网络连接到Dify API服务正常
4. 根据实际需求配置不同工作流的API密钥
