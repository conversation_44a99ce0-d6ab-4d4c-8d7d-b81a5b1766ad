# 环境配置示例文件
# 复制此文件为 .env 并根据实际情况修改配置值

# 基础配置
ENVIRONMENT=development
DEBUG=true
VERSION=1.0.0

# 服务配置
HOST=0.0.0.0
PORT=30101

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_MAX_SIZE=10485760
LOG_FILE_BACKUP_COUNT=5

# Dify配置 - 默认配置（向后兼容）
DIFY_API_KEY=your_default_dify_api_key_here
DIFY_API_URL=https://api.dify.ai
DIFY_USER=admin

# Dify配置 - SOP工作流专用（可选，如果不配置则使用默认配置）
DIFY_SOP_API_KEY=your_sop_dify_api_key_here
DIFY_SOP_API_URL=https://api.dify.ai
DIFY_SOP_USER=admin

# Dify配置 - SOP Type1工作流专用 (file_type=1)（可选，如果不配置则使用SOP或默认配置）
DIFY_SOP_TYPE1_API_KEY=your_sop_type1_dify_api_key_here
DIFY_SOP_TYPE1_API_URL=https://api.dify.ai
DIFY_SOP_TYPE1_USER=admin

# Dify配置 - SOP Type2工作流专用 (file_type=2)（可选，如果不配置则使用SOP或默认配置）
DIFY_SOP_TYPE2_API_KEY=your_sop_type2_dify_api_key_here
DIFY_SOP_TYPE2_API_URL=https://api.dify.ai
DIFY_SOP_TYPE2_USER=admin
