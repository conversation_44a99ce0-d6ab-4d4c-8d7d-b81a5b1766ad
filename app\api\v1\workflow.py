from typing import Any, Dict

from fastapi import APIRouter, HTTPException

from app.dependencies.common import get_dify_service_by_type
from app.enums.workflow_types import WorkflowType

router = APIRouter()


@router.get("/types", summary="获取所有支持的工作流类型")
async def get_workflow_types() -> Dict[str, Any]:
    """获取所有支持的工作流类型"""
    return {
        "workflow_types": WorkflowType.get_all_types(),
        "descriptions": {
            WorkflowType.SOP: "SOP标准作业程序工作流",
            WorkflowType.DEFAULT: "默认工作流",
        },
    }


@router.get("/config/{workflow_type}", summary="获取指定工作流的配置信息")
async def get_workflow_config(workflow_type: str) -> Dict[str, Any]:
    """获取指定工作流的配置信息（不包含敏感信息）"""
    if workflow_type not in WorkflowType.get_all_types():
        raise HTTPException(
            status_code=400, detail=f"不支持的工作流类型: {workflow_type}"
        )

    try:
        dify_service = get_dify_service_by_type(workflow_type)
        return {
            "workflow_type": workflow_type,
            "api_url": dify_service.base_url,
            "user": dify_service.user,
            "api_key_configured": bool(dify_service.api_key),
            "status": "configured" if dify_service.api_key else "not_configured",
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取工作流配置失败: {str(e)}")
